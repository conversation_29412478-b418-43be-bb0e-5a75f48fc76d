{"name": "@expo/vector-icons", "sideEffects": false, "version": "14.1.0", "description": "Built-in support for popular icon fonts and the tooling to create your own Icon components from your font and glyph map. This is a wrapper around react-native-vector-icons to make it compatible with Expo.", "main": "build/IconsLazy.js", "module": "build/Icons.js", "types": "build/Icons.d.ts", "scripts": {"copy-vendor": "rm -rf build/vendor && cp -R src/vendor build/vendor", "generate-lazy": "expo-module babel --config-file ./babel.config.build.js --source-maps --out-file build/IconsLazy.js build/Icons.js", "build": "EXPO_NONINTERACTIVE=1 expo-module build && npm run generate-lazy && npm run copy-vendor", "build:dev": "expo-module build", "clean": "expo-module clean", "test": "expo-module test", "lint": "expo-module lint", "prepare": "expo-module prepare && npm run generate-lazy && npm run copy-vendor", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "repository": {"type": "git", "url": "https://github.com/expo/vector-icons.git"}, "keywords": ["expo"], "author": "Brent <PERSON>", "contributors": ["<PERSON> <<EMAIL>> (https://github.com/evanbacon)"], "license": "MIT", "bugs": {"url": "https://github.com/expo/vector-icons/issues"}, "jest": {"preset": "expo-module-scripts"}, "homepage": "https://expo.github.io/vector-icons", "devDependencies": {"@babel/core": "^7.23.7", "@types/react-native": "~0.64", "eslint": "^8.56.0", "eslint-config-universe": "^12.0.0", "expo-font": "^11.10.0", "expo-module-scripts": "^3.4.0", "prettier": "^3.1.1"}, "dependencies": {}, "peerDependencies": {"expo-font": "*", "react": "*", "react-native": "*"}}