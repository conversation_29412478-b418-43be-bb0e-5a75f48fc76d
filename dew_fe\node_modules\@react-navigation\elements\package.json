{"name": "@react-navigation/elements", "description": "UI Components for React Navigation", "version": "2.6.3", "keywords": ["react-native", "react-navigation", "ios", "android"], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/react-navigation/react-navigation.git", "directory": "packages/elements"}, "bugs": {"url": "https://github.com/react-navigation/react-navigation/issues"}, "homepage": "https://reactnavigation.org", "main": "./lib/module/index.js", "types": "./lib/typescript/src/index.d.ts", "exports": {".": {"source": "./src/index.tsx", "types": "./lib/typescript/src/index.d.ts", "default": "./lib/module/index.js"}, "./package.json": "./package.json"}, "files": ["src", "lib", "!**/__tests__"], "sideEffects": false, "publishConfig": {"access": "public"}, "scripts": {"prepack": "bob build", "clean": "del lib"}, "dependencies": {"color": "^4.2.3", "use-latest-callback": "^0.2.4", "use-sync-external-store": "^1.5.0"}, "devDependencies": {"@jest/globals": "^30.0.0", "@react-native-masked-view/masked-view": "0.3.2", "@react-navigation/native": "^7.1.17", "@testing-library/react-native": "^13.2.0", "@types/react": "~19.0.10", "@types/use-sync-external-store": "^1.5.0", "del-cli": "^6.0.0", "react": "19.0.0", "react-native": "0.79.3", "react-native-builder-bob": "^0.40.12", "react-test-renderer": "19.0.0", "typescript": "^5.8.3"}, "peerDependencies": {"@react-native-masked-view/masked-view": ">= 0.2.0", "@react-navigation/native": "^7.1.17", "react": ">= 18.2.0", "react-native": "*", "react-native-safe-area-context": ">= 4.0.0"}, "peerDependenciesMeta": {"@react-native-masked-view/masked-view": {"optional": true}}, "react-native-builder-bob": {"source": "src", "output": "lib", "targets": [["module", {"esm": true}], ["typescript", {"project": "tsconfig.build.json"}]]}, "gitHead": "c8c001f3044013e16977fdff55d1a6aaa3c41d3f"}