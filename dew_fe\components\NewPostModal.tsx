import React, { useState } from 'react';
import {
  Modal,
  View,
  Text,
  TextInput,
  Pressable,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Image,
} from 'react-native';
// import * as ImagePicker from 'expo-image-picker';
import Colors from '../constants/Colors';
import { supabaseWithRefresh } from '../utils/supabaseWithRefresh';
import { useUserId } from '../hooks/useAuthWithRefresh';
import SubmitCard from './SubmitCard';
import { useSubmitCard, SUBMIT_CARD_PRESETS } from '../hooks/useSubmitCard';

interface NewPostModalProps {
  visible: boolean;
  onClose: () => void;
  onPostCreated?: () => void; // Optional callback when post is successfully created
}

const NewPostModal: React.FC<NewPostModalProps> = ({ visible, onClose, onPostCreated }) => {
  const userId = useUserId();
  const {
    isVisible: submitCardVisible,
    config: submitCardConfig,
    hideSubmitCard,
    showSuccess,
    showError,
    showWarning,
  } = useSubmitCard();

  const [content, setContent] = useState('');
  const [selectedMood, setSelectedMood] = useState('');
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const moods = [
    { emoji: '😊', label: 'Happy', value: 'happy' },
    { emoji: '💪', label: 'Strong', value: 'strong' },
    { emoji: '🤔', label: 'Thoughtful', value: 'thoughtful' },
    { emoji: '😌', label: 'Peaceful', value: 'peaceful' },
    { emoji: '😰', label: 'Struggling', value: 'struggling' },
    { emoji: '🎉', label: 'Celebrating', value: 'celebrating' },
  ];

  const pickImage = async () => {
    showWarning('Coming Soon', 'Image upload feature will be available after the next app update!', {
      emoji: '📸'
    });
    // try {
    //   // Request permission
    //   const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();
    //
    //   if (permissionResult.granted === false) {
    //     showError('Permission Required', 'Permission to access camera roll is required!');
    //     return;
    //   }

    //   // Launch image picker
    //   const result = await ImagePicker.launchImageLibraryAsync({
    //     mediaTypes: ImagePicker.MediaTypeOptions.Images,
    //     allowsEditing: true,
    //     aspect: [4, 3],
    //     quality: 0.8,
    //   });

    //   if (!result.canceled) {
    //     setSelectedImage(result.assets[0].uri);
    //   }
    // } catch (error) {
    //   showError('Error', 'Failed to pick image. Please try again.');
    // }
  };

  const removeImage = () => {
    setSelectedImage(null);
  };

  const handleSubmit = async () => {
    if (!content.trim()) {
      showError('Missing Content', 'Please write something to share with the community.', {
        emoji: '✍️'
      });
      return;
    }



    if (!userId) {
      showWarning(
        SUBMIT_CARD_PRESETS.LOGIN_REQUIRED.title,
        SUBMIT_CARD_PRESETS.LOGIN_REQUIRED.message,
        { emoji: SUBMIT_CARD_PRESETS.LOGIN_REQUIRED.emoji }
      );
      return;
    }

    setIsSubmitting(true);

    try {
      const postData = {
        user_id: userId,
        content: content.trim(),
        mood: selectedMood || null,
        image_url: selectedImage || null,
      };

      console.log("🚀 INSERTING POST:", postData);

      // Insert post into Supabase with automatic token refresh
      const { data, error } = await supabaseWithRefresh
        .from('posts')
        .insert(postData)
        .select();

      if (error) {
        console.error("❌ POST INSERT ERROR:", error);
        showError(
          SUBMIT_CARD_PRESETS.SAVE_ERROR.title,
          "Failed to create your post. Please try again.",
          { emoji: SUBMIT_CARD_PRESETS.SAVE_ERROR.emoji }
        );
      } else {
        console.log("✅ POST INSERT SUCCESS:", data);

        // Call the optional callback for any additional handling
        onPostCreated?.();

        showSuccess(
          "Post Created!",
          "🎉 Your post has been shared with the community!",
          {
            emoji: '📝',
            onAction: () => {
              // Reset form
              setContent('');
              setSelectedMood('');
              setSelectedImage(null);
              onClose();
              hideSubmitCard();
            }
          }
        );
      }
    } catch (error) {
      console.error("❌ UNEXPECTED ERROR CREATING POST:", error);
      showError(
        SUBMIT_CARD_PRESETS.SAVE_ERROR.title,
        "Failed to create your post. Please try again.",
        { emoji: SUBMIT_CARD_PRESETS.SAVE_ERROR.emoji }
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    setContent('');
    setSelectedMood('');
    setSelectedImage(null);
    onClose();
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={handleClose}
    >
      <KeyboardAvoidingView 
        style={{ flex: 1 }} 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <View style={{ flex: 1, backgroundColor: Colors.background.primary }}>
          {/* Header */}
          <View className="flex-row justify-between items-center p-5 pt-12 border-b border-white/10">
            <Pressable onPress={handleClose}>
              <Text style={{ color: Colors.text.secondary }} className="text-lg">Cancel</Text>
            </Pressable>
            <Text style={{ color: Colors.text.primary }} className="text-xl font-bold">
              New Post
            </Text>
            <Pressable 
              onPress={handleSubmit}
              disabled={isSubmitting}
              className={`py-2 px-4 rounded-xl ${isSubmitting ? 'opacity-50' : ''}`}
              style={{ backgroundColor: Colors.brand.primary }}
            >
              <Text className="text-white font-medium">
                {isSubmitting ? 'Posting...' : 'Post'}
              </Text>
            </Pressable>
          </View>

          <ScrollView className="flex-1 p-5">
            {/* Mood Selection */}
            <View className="mb-6">
              <Text style={{ color: Colors.text.primary }} className="text-lg font-medium mb-3">
                How are you feeling? 💭
              </Text>
              <View className="flex-row flex-wrap gap-3">
                {moods.map((mood) => (
                  <Pressable
                    key={mood.value}
                    onPress={() => setSelectedMood(mood.value === selectedMood ? '' : mood.value)}
                    className={`rounded-2xl p-3 shadow-lg shadow-black/50 ${
                      selectedMood === mood.value ? 'border-2' : 'border'
                    }`}
                    style={{ 
                      backgroundColor: Colors.background.elevated,
                      borderColor: selectedMood === mood.value 
                        ? Colors.brand.primary 
                        : 'rgba(255,255,255,0.03)'
                    }}
                  >
                    <Text className="text-2xl text-center mb-1">{mood.emoji}</Text>
                    <Text 
                      style={{ color: Colors.text.secondary }} 
                      className="text-xs text-center"
                    >
                      {mood.label}
                    </Text>
                  </Pressable>
                ))}
              </View>
            </View>

            {/* Image Upload */}
            <View className="mb-6">
              <Text style={{ color: Colors.text.primary }} className="text-lg font-medium mb-3">
                Add a photo (optional) 📸
              </Text>
              
              {selectedImage ? (
                <View 
                  className="rounded-2xl shadow-lg shadow-black/50 overflow-hidden"
                  style={{ 
                    backgroundColor: Colors.background.elevated,
                    borderWidth: 1,
                    borderColor: 'rgba(255,255,255,0.03)'
                  }}
                >
                  <Image 
                    source={{ uri: selectedImage }} 
                    className="w-full h-48"
                    style={{ resizeMode: 'cover' }}
                  />
                  <View className="p-3 flex-row justify-between items-center">
                    <Text style={{ color: Colors.text.secondary }} className="text-sm">
                      Image selected
                    </Text>
                    <Pressable
                      onPress={removeImage}
                      className="py-1 px-3 rounded-lg"
                      style={{ backgroundColor: 'rgba(239, 68, 68, 0.2)' }}
                    >
                      <Text style={{ color: '#ef4444' }} className="text-sm font-medium">
                        Remove
                      </Text>
                    </Pressable>
                  </View>
                </View>
              ) : (
                <Pressable
                  onPress={pickImage}
                  className="rounded-2xl p-6 shadow-lg shadow-black/50 border-2 border-dashed"
                  style={{ 
                    backgroundColor: Colors.background.elevated,
                    borderColor: 'rgba(255,255,255,0.1)'
                  }}
                >
                  <View className="items-center">
                    <Text className="text-4xl mb-2">📸</Text>
                    <Text style={{ color: Colors.text.primary }} className="font-medium mb-1">
                      Add Photo
                    </Text>
                    <Text style={{ color: Colors.text.secondary }} className="text-sm text-center">
                      Share a moment from your journey
                    </Text>
                  </View>
                </Pressable>
              )}
            </View>

            {/* Content Input */}
            <View className="mb-6">
              <Text style={{ color: Colors.text.primary }} className="text-lg font-medium mb-3">
                Share with the community 💬
              </Text>
              <View 
                className="rounded-2xl p-4 shadow-lg shadow-black/50"
                style={{ 
                  backgroundColor: Colors.background.elevated,
                  borderWidth: 1,
                  borderColor: 'rgba(255,255,255,0.03)'
                }}
              >
                <TextInput
                  value={content}
                  onChangeText={setContent}
                  placeholder="What's on your mind? Share your journey, ask for support, or celebrate a milestone..."
                  placeholderTextColor={Colors.text.secondary}
                  style={{ color: Colors.text.primary }}
                  className="text-lg min-h-[120px]"
                  multiline
                  textAlignVertical="top"
                />
              </View>
              <Text 
                style={{ color: Colors.text.secondary }} 
                className="text-sm mt-2 ml-2"
              >
                {content.length}/500 characters
              </Text>
            </View>

            {/* Community Guidelines Reminder */}
            <View 
              className="rounded-2xl p-4 mb-6"
              style={{ backgroundColor: 'rgba(34, 197, 94, 0.1)' }}
            >
              <Text style={{ color: Colors.brand.primary }} className="font-medium mb-2">
                💚 Community Guidelines
              </Text>
              <Text style={{ color: Colors.text.secondary }} className="text-sm leading-5">
                • Be supportive and respectful{'\n'}
                • Share your authentic experience{'\n'}
                • No judgment - we&apos;re all on our own journey{'\n'}
                • Keep personal information private
              </Text>
            </View>
          </ScrollView>
        </View>
      </KeyboardAvoidingView>

      {/* Submit Card */}
      {submitCardConfig && (
        <SubmitCard
          visible={submitCardVisible}
          type={submitCardConfig.type}
          title={submitCardConfig.title}
          message={submitCardConfig.message}
          onClose={hideSubmitCard}
          onAction={submitCardConfig.onAction}
          actionText={submitCardConfig.actionText}
          autoClose={submitCardConfig.autoClose}
          autoCloseDelay={submitCardConfig.autoCloseDelay}
          emoji={submitCardConfig.emoji}
        />
      )}
    </Modal>
  );
};

export default NewPostModal;
