# Supabase Refresh Token API Integration

This document explains the comprehensive refresh token API integration implemented in the app, providing automatic token refresh, enhanced security, and seamless user experience.

## 🚀 Overview

The refresh token system automatically handles token expiration, refreshes access tokens when needed, and provides a robust authentication layer for your Supabase integration.

## 📁 File Structure

```
dew_fe/
├── utils/
│   ├── tokenManager.ts          # Core token management singleton
│   ├── supabaseWithRefresh.ts   # Enhanced Supabase client with auto-refresh
│   └── supabaseAuth.tsx         # Updated Google Sign-In component
├── hooks/
│   ├── useAuthWithRefresh.ts    # Enhanced authentication hook
│   └── useAuth.ts               # Legacy hook (still functional)
├── components/
│   └── RefreshTokenExample.tsx  # Demo component showing usage
└── docs/
    └── REFRESH_TOKEN_API.md     # This documentation
```

## 🔧 Core Components

### 1. TokenManager (`utils/tokenManager.ts`)

A singleton class that handles all token operations:

```typescript
import { tokenManager } from '../utils/tokenManager';

// Check if user is authenticated (auto-refreshes if needed)
const isAuth = await tokenManager.isAuthenticated();

// Get a valid session (refreshes if expired)
const session = await tokenManager.getValidSession();

// Manually refresh token
const newSession = await tokenManager.refreshToken();

// Clear all tokens
await tokenManager.clearTokens();

// Sign out completely
await tokenManager.signOut();
```

### 2. Enhanced Supabase Client (`utils/supabaseWithRefresh.ts`)

Drop-in replacement for the standard Supabase client with automatic token refresh:

```typescript
import { supabaseWithRefresh } from '../utils/supabaseWithRefresh';

// All database operations automatically handle token refresh
const { data, error } = await supabaseWithRefresh
  .from('posts')
  .select('*')
  .eq('user_id', userId);

// Authentication methods with automatic token saving
const { data, error } = await supabaseWithRefresh.auth.signInWithPassword({
  email: '<EMAIL>',
  password: 'password'
});
```

### 3. Enhanced Authentication Hook (`hooks/useAuthWithRefresh.ts`)

Comprehensive authentication state management:

```typescript
import { useAuthWithRefresh, useUserId, useRequireAuth } from '../hooks/useAuthWithRefresh';

// Full authentication state and actions
const { 
  user, 
  session, 
  loading, 
  isAuthenticated, 
  signOut, 
  refreshSession, 
  checkAuthStatus 
} = useAuthWithRefresh();

// Just get user ID (simpler)
const userId = useUserId();

// Require authentication (auto-redirects if not authenticated)
const auth = useRequireAuth();
```

## 🔄 How Token Refresh Works

### Automatic Refresh Triggers

1. **API Calls**: When making database queries, if a 401/JWT error occurs, tokens are automatically refreshed
2. **Session Checks**: When getting the current session, expired tokens are refreshed
3. **Scheduled Refresh**: Tokens are automatically refreshed 5 minutes before expiration
4. **App Initialization**: On app start, token validity is checked and refreshed if needed

### Refresh Flow

```mermaid
graph TD
    A[API Call] --> B{Token Valid?}
    B -->|Yes| C[Execute Request]
    B -->|No| D[Refresh Token]
    D --> E{Refresh Success?}
    E -->|Yes| F[Retry Original Request]
    E -->|No| G[Clear Tokens & Redirect to Login]
    F --> C
    C --> H[Return Response]
    G --> I[Show Login Screen]
```

## 🛡️ Security Features

### Token Storage
- **Secure Storage**: All tokens stored using Expo SecureStore
- **Automatic Cleanup**: Tokens cleared on sign out or refresh failure
- **No Sensitive Data in Memory**: Tokens retrieved fresh when needed

### Error Handling
- **Invalid Tokens**: Automatically cleared and user redirected to login
- **Network Errors**: Graceful fallback with user notification
- **Concurrent Requests**: Prevents multiple simultaneous refresh attempts

### Session Validation
- **Expiration Checking**: Tokens validated before use with 5-minute buffer
- **Real-time Updates**: Auth state changes reflected immediately across app
- **Automatic Cleanup**: Expired sessions automatically refreshed or cleared

## 📱 Usage Examples

### Basic Authentication Check

```typescript
import { useAuthWithRefresh } from '../hooks/useAuthWithRefresh';

const MyComponent = () => {
  const { isAuthenticated, loading } = useAuthWithRefresh();

  if (loading) return <LoadingSpinner />;
  if (!isAuthenticated) return <LoginPrompt />;
  
  return <AuthenticatedContent />;
};
```

### Making API Calls

```typescript
import { supabaseWithRefresh } from '../utils/supabaseWithRefresh';

const createPost = async (postData) => {
  try {
    // Automatically handles token refresh if needed
    const { data, error } = await supabaseWithRefresh
      .from('posts')
      .insert(postData)
      .select();
      
    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Failed to create post:', error);
    throw error;
  }
};
```

### Manual Token Management

```typescript
import { tokenManager } from '../utils/tokenManager';

const handleTokenRefresh = async () => {
  try {
    const session = await tokenManager.refreshToken();
    if (session) {
      console.log('Token refreshed successfully');
    } else {
      console.log('Refresh failed, user needs to log in');
    }
  } catch (error) {
    console.error('Refresh error:', error);
  }
};
```

## 🔧 Migration Guide

### From Legacy useAuth Hook

**Before:**
```typescript
import { useAuth } from '../hooks/useAuth';

const { userId } = useAuth();
```

**After:**
```typescript
import { useUserId } from '../hooks/useAuthWithRefresh';

const userId = useUserId();
```

### From Direct Supabase Client

**Before:**
```typescript
import { supabase } from '../lib/supabase';

const { data, error } = await supabase.from('table').select('*');
```

**After:**
```typescript
import { supabaseWithRefresh } from '../utils/supabaseWithRefresh';

const { data, error } = await supabaseWithRefresh.from('table').select('*');
```

## 🐛 Troubleshooting

### Common Issues

1. **"Authentication failed" errors**
   - Check if tokens are properly stored in SecureStore
   - Verify Supabase project configuration
   - Ensure refresh token is valid

2. **Infinite refresh loops**
   - Check for concurrent refresh attempts
   - Verify token expiration logic
   - Clear all tokens and re-authenticate

3. **Session not persisting**
   - Ensure SecureStore permissions are granted
   - Check if tokens are being cleared unexpectedly
   - Verify auth state listener is properly set up

### Debug Mode

Enable detailed logging by setting up console logs:

```typescript
// In your app initialization
console.log('🔐 Auth Debug Mode Enabled');

// The token manager and auth hooks already include comprehensive logging
// Look for logs prefixed with 🔐, 🔄, ✅, and ❌
```

## 🚀 Best Practices

1. **Always use enhanced hooks**: Prefer `useAuthWithRefresh` over legacy `useAuth`
2. **Use supabaseWithRefresh**: Replace direct supabase client usage
3. **Handle loading states**: Always check `loading` state in components
4. **Graceful error handling**: Implement proper error boundaries
5. **Test token expiration**: Test app behavior when tokens expire
6. **Monitor refresh frequency**: Avoid excessive refresh attempts

## 📊 Performance Considerations

- **Singleton Pattern**: TokenManager uses singleton to prevent multiple instances
- **Request Deduplication**: Multiple simultaneous refresh requests are deduplicated
- **Efficient Storage**: Minimal SecureStore operations with batch updates
- **Memory Management**: Tokens not kept in memory longer than necessary
- **Background Refresh**: Automatic refresh happens before expiration to prevent interruptions

## 🔮 Future Enhancements

- **Offline Support**: Handle token refresh when app comes back online
- **Biometric Authentication**: Add biometric unlock for stored tokens
- **Token Rotation**: Implement more frequent token rotation for enhanced security
- **Analytics**: Add token refresh analytics and monitoring
- **Background Sync**: Sync authentication state across multiple app instances
