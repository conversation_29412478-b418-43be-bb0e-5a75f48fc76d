import React, { useState, useEffect, useCallback } from 'react';
import {
  Modal,
  View,
  Text,
  TextInput,
  Pressable,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
  RefreshControl,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Colors from '../constants/Colors';
import { supabaseWithRefresh } from '../utils/supabaseWithRefresh';
import { useUserId } from '../hooks/useAuthWithRefresh';
import SubmitCard from './SubmitCard';
import { useSubmitCard, SUBMIT_CARD_PRESETS } from '../hooks/useSubmitCard';

interface Comment {
  id: string;
  content: string;
  created_at: string;
  user_id: string;
  userName: string;
  userInitials: string;
  timeAgo: string;
}

interface CommentsModalProps {
  visible: boolean;
  onClose: () => void;
  postId: string;
  postTitle?: string;
}

const CommentsModal: React.FC<CommentsModalProps> = ({
  visible,
  onClose,
  postId
}) => {
  const userId = useUserId();
  const {
    isVisible: submitCardVisible,
    config: submitCardConfig,
    hideSubmitCard,
    showSuccess,
    showError,
    showWarning,
  } = useSubmitCard();

  const [comments, setComments] = useState<Comment[]>([]);
  const [newComment, setNewComment] = useState('');
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  // Helper function to get user initials from name
  const getUserInitials = (name: string): string => {
    if (!name) return '👤';
    const names = name.split(' ');
    if (names.length >= 2) {
      return `${names[0][0]}${names[1][0]}`.toUpperCase();
    }
    return names[0][0].toUpperCase();
  };

  // Helper function to format time ago
  const getTimeAgo = (createdAt: string): string => {
    const now = new Date();
    const created = new Date(createdAt);
    const diffInMinutes = Math.floor((now.getTime() - created.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d ago`;
    
    return created.toLocaleDateString();
  };

  // Fetch comments for the post
  const fetchComments = useCallback(async (showLoader = true) => {
    if (!postId) return;

    if (showLoader) setLoading(true);
    try {
      console.log('🔄 Fetching comments for post:', postId);

      const { data: commentsData, error } = await supabaseWithRefresh
        .from('post_comments')
        .select('*')
        .eq('post_id', postId)
        .order('created_at', { ascending: true });

      if (error) {
        console.error('❌ Error fetching comments:', error);
        showError('Failed to Load', 'Could not load comments. Please try again.', {
          emoji: '💬'
        });
        return;
      }

      if (!commentsData || commentsData.length === 0) {
        setComments([]);
        return;
      }

      // Transform comments data (mock user data for now)
      const transformedComments: Comment[] = commentsData.map((comment: any) => {
        // Generate mock user data - in real app, you'd fetch from a users table
        const mockNames = ['Alex Johnson', 'Sarah Miller', 'Mike Kennedy', 'Emma Davis', 'John Smith', 'Lisa Brown'];
        const nameIndex = comment.user_id.split('').reduce((acc: number, char: string) => acc + char.charCodeAt(0), 0) % mockNames.length;
        const userName = mockNames[nameIndex];

        return {
          id: comment.id,
          content: comment.content,
          created_at: comment.created_at,
          user_id: comment.user_id,
          userName,
          userInitials: getUserInitials(userName),
          timeAgo: getTimeAgo(comment.created_at)
        };
      });

      setComments(transformedComments);
      console.log(`✅ Loaded ${transformedComments.length} comments`);

    } catch (error) {
      console.error('❌ Unexpected error fetching comments:', error);
      showError('Unexpected Error', 'Something went wrong while loading comments.', {
        emoji: '⚠️'
      });
    } finally {
      if (showLoader) setLoading(false);
    }
  }, [postId, showError]);

  // Refresh comments
  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await fetchComments(false);
    setRefreshing(false);
  }, [fetchComments]);

  // Submit new comment
  const handleSubmitComment = useCallback(async () => {
    if (!newComment.trim()) {
      showWarning('Empty Comment', 'Please write something before posting.', {
        emoji: '✍️'
      });
      return;
    }

    if (!userId) {
      showWarning('Login Required', 'Please log in to post a comment.', {
        emoji: '🔐'
      });
      return;
    }

    if (!postId) {
      showError('Invalid Post', 'Cannot post comment to this post.', {
        emoji: '❌'
      });
      return;
    }

    setSubmitting(true);
    try {
      console.log('🔄 Submitting comment...');

      const { error } = await supabaseWithRefresh
        .from('post_comments')
        .insert({
          post_id: postId,
          user_id: userId,
          content: newComment.trim()
        });

      if (error) {
        console.error('❌ Error submitting comment:', error);
        showError('Failed to Post', 'Could not post your comment. Please try again.', {
          emoji: '💬'
        });
        return;
      }

      console.log('✅ Comment submitted successfully');

      // Clear input and refresh comments
      setNewComment('');
      await fetchComments(false);

      showSuccess('Comment Posted!', 'Your comment has been added successfully.', {
        emoji: '💬',
        autoClose: true,
        autoCloseDelay: 2000
      });

    } catch (error) {
      console.error('❌ Unexpected error submitting comment:', error);
      showError('Unexpected Error', 'Something went wrong while posting your comment.', {
        emoji: '⚠️'
      });
    } finally {
      setSubmitting(false);
    }
  }, [newComment, userId, postId, fetchComments, showWarning, showError, showSuccess]);

  // Fetch comments when modal opens
  useEffect(() => {
    if (visible && postId) {
      fetchComments();
    }
  }, [visible, postId, fetchComments]);

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <KeyboardAvoidingView 
        style={{ flex: 1 }} 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <View style={{ flex: 1, backgroundColor: Colors.background.primary }}>
          {/* Header */}
          <View
            className="flex-row items-center justify-between p-4 border-b"
            style={{
              backgroundColor: Colors.background.elevated,
              borderBottomColor: 'rgba(255,255,255,0.1)',
              shadowColor: '#000',
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 0.1,
              shadowRadius: 4,
              elevation: 3
            }}
          >
            <View className="flex-row items-center">
              <Text style={{ color: Colors.text.primary }} className="text-lg font-bold mr-2">
                Comments
              </Text>
              {comments.length > 0 && (
                <View
                  className="px-2 py-1 rounded-full"
                  style={{ backgroundColor: Colors.brand.primary + '20' }}
                >
                  <Text style={{ color: Colors.brand.primary }} className="text-xs font-bold">
                    {comments.length}
                  </Text>
                </View>
              )}
            </View>
            <Pressable
              onPress={onClose}
              className="p-2 rounded-full"
              style={{ backgroundColor: 'rgba(255,255,255,0.1)' }}
            >
              <Ionicons name="close" size={24} color={Colors.text.primary} />
            </Pressable>
          </View>

          {/* Comments List */}
          <ScrollView
            className="flex-1"
            contentContainerStyle={{ padding: 16 }}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={onRefresh}
                tintColor={Colors.brand.primary}
                colors={[Colors.brand.primary]}
              />
            }
            showsVerticalScrollIndicator={false}
          >
            {loading ? (
              <View className="flex-1 justify-center items-center py-20">
                <ActivityIndicator size="large" color={Colors.brand.primary} />
                <Text style={{ color: Colors.text.secondary }} className="mt-4 text-center">
                  Loading comments...
                </Text>
              </View>
            ) : comments.length === 0 ? (
              <View className="flex-1 justify-center items-center py-20">
                <View
                  className="w-20 h-20 rounded-full items-center justify-center mb-6"
                  style={{ backgroundColor: Colors.background.elevated }}
                >
                  <Text className="text-4xl">💬</Text>
                </View>
                <Text style={{ color: Colors.text.primary }} className="text-xl font-bold mb-2 text-center">
                  No comments yet
                </Text>
                <Text style={{ color: Colors.text.secondary }} className="text-center px-8 leading-6">
                  Be the first to share your thoughts and start the conversation!
                </Text>
              </View>
            ) : (
              <View className="space-y-4">
                {comments.map((comment, index) => (
                  <View
                    key={comment.id}
                    className="p-4 rounded-2xl shadow-sm"
                    style={{
                      backgroundColor: Colors.background.elevated,
                      borderWidth: 1,
                      borderColor: 'rgba(255,255,255,0.05)',
                      marginBottom: index === comments.length - 1 ? 0 : 12
                    }}
                  >
                    <View className="flex-row items-start mb-3">
                      <View
                        className="w-10 h-10 rounded-full items-center justify-center mr-3 shadow-sm"
                        style={{
                          backgroundColor: Colors.brand.primary + '20',
                          borderWidth: 2,
                          borderColor: Colors.brand.primary + '40'
                        }}
                      >
                        <Text style={{ color: Colors.brand.primary }} className="text-sm font-bold">
                          {comment.userInitials}
                        </Text>
                      </View>
                      <View className="flex-1">
                        <View className="flex-row items-center justify-between">
                          <Text style={{ color: Colors.text.primary }} className="font-semibold text-base">
                            {comment.userName}
                          </Text>
                          <Text style={{ color: Colors.text.secondary }} className="text-xs">
                            {comment.timeAgo}
                          </Text>
                        </View>
                        <Text style={{ color: Colors.text.primary }} className="leading-6 mt-2">
                          {comment.content}
                        </Text>
                      </View>
                    </View>
                  </View>
                ))}
              </View>
            )}
          </ScrollView>

          {/* Comment Input */}
          <View
            className="p-4 border-t"
            style={{
              backgroundColor: Colors.background.elevated,
              borderTopColor: 'rgba(255,255,255,0.1)',
              shadowColor: '#000',
              shadowOffset: { width: 0, height: -2 },
              shadowOpacity: 0.1,
              shadowRadius: 4,
              elevation: 5
            }}
          >
            {/* Character count indicator */}
            {newComment.length > 0 && (
              <View className="flex-row justify-between items-center mb-2">
                <Text style={{ color: Colors.text.secondary }} className="text-xs">
                  {userId ? 'Share your thoughts...' : 'Please log in to comment'}
                </Text>
                <Text
                  style={{
                    color: newComment.length > 800 ? Colors.status.error : Colors.text.secondary
                  }}
                  className="text-xs"
                >
                  {newComment.length}/1000
                </Text>
              </View>
            )}

            <View className="flex-row items-end space-x-3">
              {/* User Avatar */}
              {userId && (
                <View
                  className="w-8 h-8 rounded-full items-center justify-center mb-2"
                  style={{
                    backgroundColor: Colors.brand.primary + '20',
                    borderWidth: 1,
                    borderColor: Colors.brand.primary + '40'
                  }}
                >
                  <Text style={{ color: Colors.brand.primary }} className="text-xs font-bold">
                    You
                  </Text>
                </View>
              )}

              {/* Input Field */}
              <TextInput
                value={newComment}
                onChangeText={setNewComment}
                placeholder={userId ? "Write a thoughtful comment..." : "Please log in to comment"}
                placeholderTextColor={Colors.text.secondary}
                editable={!!userId}
                style={{
                  color: Colors.text.primary,
                  backgroundColor: Colors.background.primary,
                  borderColor: newComment.length > 800 ? Colors.status.error : 'rgba(255,255,255,0.1)',
                  borderWidth: 1.5,
                  borderRadius: 20,
                  paddingHorizontal: 16,
                  paddingVertical: 12,
                  flex: 1,
                  marginRight: 12,
                  maxHeight: 120,
                  minHeight: 44,
                  fontSize: 16,
                  lineHeight: 20
                }}
                multiline
                textAlignVertical="top"
                maxLength={1000}
              />

              {/* Send Button */}
              <Pressable
                onPress={handleSubmitComment}
                disabled={!newComment.trim() || submitting || !userId || newComment.length > 1000}
                className="p-3 rounded-full shadow-sm"
                style={{
                  backgroundColor: (newComment.trim() && userId && newComment.length <= 1000)
                    ? Colors.brand.primary
                    : Colors.background.secondary,
                  opacity: submitting ? 0.6 : 1,
                  shadowColor: Colors.brand.primary,
                  shadowOffset: { width: 0, height: 2 },
                  shadowOpacity: (newComment.trim() && userId) ? 0.3 : 0,
                  shadowRadius: 4,
                  elevation: (newComment.trim() && userId) ? 3 : 0
                }}
              >
                {submitting ? (
                  <ActivityIndicator size="small" color="white" />
                ) : (
                  <Ionicons
                    name="send"
                    size={20}
                    color={(newComment.trim() && userId && newComment.length <= 1000) ? 'white' : Colors.text.secondary}
                  />
                )}
              </Pressable>
            </View>
          </View>
        </View>
      </KeyboardAvoidingView>

      {/* Submit Card */}
      {submitCardConfig && (
        <SubmitCard
          visible={submitCardVisible}
          type={submitCardConfig.type}
          title={submitCardConfig.title}
          message={submitCardConfig.message}
          onClose={hideSubmitCard}
          onAction={submitCardConfig.onAction}
          actionText={submitCardConfig.actionText}
          autoClose={submitCardConfig.autoClose}
          autoCloseDelay={submitCardConfig.autoCloseDelay}
          emoji={submitCardConfig.emoji}
        />
      )}
    </Modal>
  );
};

export default CommentsModal;
