import { useEffect, useCallback } from "react";
import { useRouter } from "expo-router";
import { View, Text, ActivityIndicator } from "react-native";
import Colors from "../../constants/Colors";
import { tokenManager } from "../../utils/tokenManager";

export default function Index() {
  const router = useRouter();

  const checkAuthStatus = useCallback(async () => {
    try {
      console.log("🔐 Checking authentication status...");

      const isAuthenticated = await tokenManager.isAuthenticated();

      if (isAuthenticated) {
        console.log("✅ User is authenticated, redirecting to dashboard");
        router.replace("/(tabs)/dashboard");
      } else {
        console.log("❌ User is not authenticated, redirecting to login");
        router.replace("/login");
      }
    } catch (error) {
      console.error("❌ Error checking auth status:", error);
      // On error, redirect to login
      router.replace("/login");
    }
  }, [router]);

  useEffect(() => {
    checkAuthStatus();
  }, [checkAuthStatus]);

  return (
    <View
      className="flex-1 justify-center items-center"
      style={{ backgroundColor: Colors.background.primary }}
    >
      <ActivityIndicator size="large" color="#22c55e" />
      <Text className="text-white mt-4 text-lg">Loading...</Text>
    </View>
  );
}
