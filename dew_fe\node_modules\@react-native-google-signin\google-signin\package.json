{"name": "@react-native-google-signin/google-signin", "version": "15.0.0", "description": "Google sign in for your react native applications", "main": "./lib/module/index.js", "types": "./lib/typescript/src/index.d.ts", "source": "./src/index.ts", "exports": {".": {"types": "./lib/typescript/src/index.d.ts", "default": "./lib/module/index.js"}, "./app.plugin.js": "./app.plugin.js", "./package.json": "./package.json"}, "files": ["src", "lib", "android", "ios", "cpp", "expo", "RNGoogleSignin.podspec", "!lib/typescript/example", "!android/build", "!ios/build", "!**/__tests__", "!**/__fixtures__", "!**/__mocks__", "app.plugin.js", "expo-module.config.json", "plugin/build", "jest/build", "README.md"], "scripts": {"start": "react-native start --reset-cache", "android": "react-native run-android", "ios": "react-native run-ios", "nuke-ios": "rm -rf example/ios/Podfile.lock && rm -rf example/ios/Pods && rm -rf example/ios/build", "build:mock": "tsc --build jest", "build:plugin": "tsc --build plugin", "clean:plugin": "expo-module clean plugin", "test": "SILENCE_MOCK_NOT_FOUND=true jest", "typescript": "tsc --noEmit", "lint": "eslint \"**/*.{js,ts,tsx}\"", "prepare": "bob build && yarn build:plugin && yarn build:mock && husky install", "release": "yarn prepare && npx semantic-release", "pods": "cd example && pod-install --quiet", "bootstrap": "yarn example && yarn && yarn pods", "prettier:check": "prettier --list-different '**/*.{js,ts,tsx}' '**/*.md'", "prettier:write": "prettier --write '**/*.{js,ts,tsx}' '**/*.md'"}, "keywords": ["react-native", "google sign in", "google login", "android creadential manager", "o<PERSON>h", "oauth2", "ios", "android", "web"], "repository": "https://github.com/react-native-google-signin/google-signin", "author": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/vonovak)", "license": "MIT", "bugs": {"url": "https://github.com/react-native-google-signin/google-signin/issues"}, "homepage": "https://github.com/react-native-google-signin/google-signin#readme", "publishConfig": {"registry": "https://registry.npmjs.org/"}, "devDependencies": {"@babel/eslint-parser": "^7.24.1", "@commitlint/cli": "^18.6.1", "@commitlint/config-conventional": "^18.6.3", "@react-native-community/cli": "15", "@react-native-community/eslint-config": "^3.2.0", "@react-native/eslint-config": "^0.76.9", "@react-native/metro-config": "^0.76.9", "@semantic-release/git": "^10.0.1", "@types/jest": "^29.5.12", "@types/react": "^18.2.79", "babel-plugin-module-resolver": "^5.0.2", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-ft-flow": "^3.0.7", "eslint-plugin-jest": "^28.5.0", "eslint-plugin-prettier": "^5.1.3", "expo": "^51.0.39", "expo-module-scripts": "^3.5.4", "husky": "^8.0.3", "jest": "^29.7.0", "patch-package": "^7.0.2", "pod-install": "^0.2.2", "prettier": "^3.2.5", "react": "18.3.1", "react-native": "^0.76.9", "react-native-builder-bob": "^0.40.10", "react-native-test-app": "3.10.22", "semantic-release": "^22.0.12", "typescript": "^5.4.5"}, "peerDependencies": {"expo": ">=52.0.40", "react": "*", "react-native": "*"}, "peerDependenciesMeta": {"expo": {"optional": true}}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "react-native-builder-bob": {"source": "src", "output": "lib", "targets": [["module", {"esm": true}], ["typescript", {"project": "tsconfig.build.json"}]]}, "codegenConfig": {"name": "RNGoogleSignInCGen", "type": "all", "jsSrcsDir": "src/spec", "android": {"javaPackageName": "com.reactnativegooglesignin"}, "ios": {"componentProvider": {"RNGoogleSignInButton": "RNGoogleSignInButtonComponentView"}}}, "packageManager": "yarn@4.1.1", "GoogleSignInPodVersion": "~> 8.0"}