import React, { useState, useEffect, useCallback } from 'react';
import { Pressable, ScrollView, Text, View, ActivityIndicator, RefreshControl } from 'react-native';
import CommunityPost from '../../components/CommunityPost';
import NewPostModal from '../../components/NewPostModal';
import CommentsModal from '../../components/CommentsModal';
import Colors from '../../constants/Colors';
import { supabaseWithRefresh } from '../../utils/supabaseWithRefresh';
import { useUserId } from '../../hooks/useAuthWithRefresh';

interface Post {
  id: string;
  userInitials: string;
  userName: string;
  timeAgo: string;
  soberDays: number;
  content: string;
  likes: number;
  supports: number;
  comments: number;
  userColor: string;
  mood?: string;
  image?: string;
  user_id: string;
  created_at: string;
  isLiked?: boolean;
  isSupported?: boolean;
}

const Community = () => {
  const userId = useUserId();
  const [showNewPostModal, setShowNewPostModal] = useState(false);
  const [showCommentsModal, setShowCommentsModal] = useState(false);
  const [selectedPostId, setSelectedPostId] = useState<string>('');
  const [posts, setPosts] = useState<Post[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Helper function to get user initials from name
  const getUserInitials = (name: string): string => {
    if (!name) return '👤';
    const names = name.split(' ');
    if (names.length >= 2) {
      return `${names[0][0]}${names[1][0]}`.toUpperCase();
    }
    return names[0][0].toUpperCase();
  };

  // Helper function to get user color based on user ID
  const getUserColor = (userId: string): string => {
    const colors = [
      'bg-blue-900/30',
      'bg-purple-900/30',
      'bg-amber-900/30',
      'bg-green-900/30',
      'bg-red-900/30',
      'bg-indigo-900/30',
      'bg-pink-900/30',
      'bg-teal-900/30'
    ];
    const index = userId.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0) % colors.length;
    return colors[index];
  };

  // Helper function to format time ago
  const getTimeAgo = (createdAt: string): string => {
    const now = new Date();
    const created = new Date(createdAt);
    const diffInMinutes = Math.floor((now.getTime() - created.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;

    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;

    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d ago`;

    return created.toLocaleDateString();
  };

  // Fetch posts from Supabase
  const fetchPosts = useCallback(async () => {
    try {
      console.log('🔄 Fetching community posts...');

      // Fetch posts with interaction counts
      const { data: postsData, error: postsError } = await supabaseWithRefresh
        .from('posts')
        .select(`
          id,
          content,
          mood,
          image_url,
          created_at,
          user_id,
          post_likes(count),
          post_supports(count),
          post_comments(count)
        `)
        .order('created_at', { ascending: false })
        .limit(20);

      if (postsError) {
        console.error('❌ Error fetching posts:', postsError);
        return;
      }

      if (!postsData || postsData.length === 0) {
        console.log('📭 No posts found');
        setPosts([]);
        return;
      }

      // Get unique user IDs to fetch user data and streaks
      const userIds = [...new Set(postsData.map((post: any) => post.user_id))] as string[];

      // Fetch user data from auth.users (this might need adjustment based on your setup)
      // For now, we'll create mock user data based on user_id
      const usersMap = new Map<string, { name: string; email: string }>();
      userIds.forEach((userId: string) => {
        // Generate mock user data - in real app, you'd fetch from a users table
        const mockNames = ['Alex Johnson', 'Sarah Miller', 'Mike Kennedy', 'Emma Davis', 'John Smith', 'Lisa Brown'];
        const nameIndex = userId.split('').reduce((acc: number, char: string) => acc + char.charCodeAt(0), 0) % mockNames.length;
        usersMap.set(userId, {
          name: mockNames[nameIndex],
          email: `user${userId.slice(-4)}@example.com`
        });
      });

      // Fetch current streaks for all users
      const { data: streaksData, error: streaksError } = await supabaseWithRefresh
        .from('pledge_current_streaks')
        .select('user_id, current_streak_started_on')
        .in('user_id', userIds);

      if (streaksError) {
        console.error('❌ Error fetching streaks:', streaksError);
      }

      // Calculate sober days for each user
      const streaksMap = new Map<string, number>();
      if (streaksData) {
        for (const streak of streaksData) {
          if (streak.current_streak_started_on) {
            const startDate = new Date(streak.current_streak_started_on);
            const today = new Date();
            const diffTime = today.getTime() - startDate.getTime();
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            streaksMap.set(streak.user_id, Math.max(0, diffDays));
          } else {
            streaksMap.set(streak.user_id, 0);
          }
        }
      }

      // Fetch current user's interactions if logged in
      let userLikes = new Set<string>();
      let userSupports = new Set<string>();

      if (userId) {
        const postIds = postsData.map((post: any) => post.id);

        // Fetch user's likes
        const { data: likesData } = await supabaseWithRefresh
          .from('post_likes')
          .select('post_id')
          .eq('user_id', userId)
          .in('post_id', postIds);

        if (likesData) {
          userLikes = new Set(likesData.map((like: any) => like.post_id));
        }

        // Fetch user's supports
        const { data: supportsData } = await supabaseWithRefresh
          .from('post_supports')
          .select('post_id')
          .eq('user_id', userId)
          .in('post_id', postIds);

        if (supportsData) {
          userSupports = new Set(supportsData.map((support: any) => support.post_id));
        }
      }

      // Transform posts data
      const transformedPosts: Post[] = postsData.map((post: any) => {
        const user = usersMap.get(post.user_id);
        const soberDays = streaksMap.get(post.user_id) || 0;

        return {
          id: post.id,
          user_id: post.user_id,
          userInitials: getUserInitials(user?.name || ''),
          userName: user?.name || 'Anonymous',
          timeAgo: getTimeAgo(post.created_at),
          soberDays,
          content: post.content,
          likes: post.post_likes?.[0]?.count || 0,
          supports: post.post_supports?.[0]?.count || 0,
          comments: post.post_comments?.[0]?.count || 0,
          userColor: getUserColor(post.user_id),
          mood: post.mood,
          image: post.image_url,
          created_at: post.created_at,
          isLiked: userLikes.has(post.id),
          isSupported: userSupports.has(post.id)
        };
      });

      setPosts(transformedPosts);
      console.log(`✅ Loaded ${transformedPosts.length} posts`);

    } catch (error) {
      console.error('❌ Unexpected error fetching posts:', error);
    }
  }, [userId]);

  // Refresh posts
  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await fetchPosts();
    setRefreshing(false);
  }, [fetchPosts]);

  // Handle post created
  const handlePostCreated = useCallback(() => {
    setShowNewPostModal(false);
    // Refresh posts to show the new post
    fetchPosts();
  }, [fetchPosts]);

  // Handle like toggle
  const handleLike = useCallback(async (postId: string) => {
    if (!userId) {
      console.log('❌ User not authenticated for like action');
      return;
    }

    try {
      const post = posts.find(p => p.id === postId);
      if (!post) return;

      if (post.isLiked) {
        // Remove like
        const { error } = await supabaseWithRefresh
          .from('post_likes')
          .delete()
          .eq('post_id', postId)
          .eq('user_id', userId);

        if (error) {
          console.error('❌ Error removing like:', error);
          return;
        }
      } else {
        // Add like
        const { error } = await supabaseWithRefresh
          .from('post_likes')
          .insert({ post_id: postId, user_id: userId });

        if (error) {
          console.error('❌ Error adding like:', error);
          return;
        }
      }

      // Update local state optimistically
      setPosts(prevPosts =>
        prevPosts.map(p =>
          p.id === postId
            ? {
                ...p,
                isLiked: !p.isLiked,
                likes: p.isLiked ? p.likes - 1 : p.likes + 1
              }
            : p
        )
      );
    } catch (error) {
      console.error('❌ Unexpected error handling like:', error);
    }
  }, [userId, posts]);

  // Handle support toggle
  const handleSupport = useCallback(async (postId: string) => {
    if (!userId) {
      console.log('❌ User not authenticated for support action');
      return;
    }

    try {
      const post = posts.find(p => p.id === postId);
      if (!post) return;

      if (post.isSupported) {
        // Remove support
        const { error } = await supabaseWithRefresh
          .from('post_supports')
          .delete()
          .eq('post_id', postId)
          .eq('user_id', userId);

        if (error) {
          console.error('❌ Error removing support:', error);
          return;
        }
      } else {
        // Add support
        const { error } = await supabaseWithRefresh
          .from('post_supports')
          .insert({ post_id: postId, user_id: userId });

        if (error) {
          console.error('❌ Error adding support:', error);
          return;
        }
      }

      // Update local state optimistically
      setPosts(prevPosts =>
        prevPosts.map(p =>
          p.id === postId
            ? {
                ...p,
                isSupported: !p.isSupported,
                supports: p.isSupported ? p.supports - 1 : p.supports + 1
              }
            : p
        )
      );
    } catch (error) {
      console.error('❌ Unexpected error handling support:', error);
    }
  }, [userId, posts]);

  // Handle comment action
  const handleComment = useCallback((postId: string) => {
    setSelectedPostId(postId);
    setShowCommentsModal(true);
  }, []);

  // Fetch posts on component mount
  useEffect(() => {
    fetchPosts().finally(() => setLoading(false));
  }, [fetchPosts]);

  return (
    <ScrollView
      className="flex-1"
      style={{ backgroundColor: Colors.background.primary }}
      refreshControl={
        <RefreshControl
          refreshing={refreshing}
          onRefresh={onRefresh}
          tintColor={Colors.brand.primary}
          colors={[Colors.brand.primary]}
        />
      }
    >
      <View className="px-5 pt-10 pb-20">
        {/* Header Section */}
        <View className="flex-row justify-between items-center mb-8">
          <View>
            <Text style={{ color: Colors.text.primary }} className="text-4xl font-bold mb-2">Community</Text>
            <Text style={{ color: Colors.text.secondary }}>Connect, share, and grow together</Text>
          </View>
          <Pressable 
            className="w-11 h-11 rounded-full items-center justify-center shadow-lg shadow-black/50" 
            style={{ 
              backgroundColor: Colors.background.elevated,
              borderWidth: 1,
              borderColor: 'rgba(255,255,255,0.03)'
            }}
          >
            <Text className="text-lg">✏️</Text>
          </Pressable>
        </View>

        {/* Community Stats */}
        <View className="flex-row justify-between mb-8">
          {['Members', 'Posts', 'Online'].map((label, index) => (
            <View 
              key={index}
              className="w-[31%] rounded-2xl p-4 shadow-lg shadow-black/50"
              style={{ 
                backgroundColor: Colors.background.elevated,
                borderWidth: 1,
                borderColor: 'rgba(255,255,255,0.03)'
              }}
            >
              <Text className="text-3xl text-center mb-2">
                {index === 0 ? '👥' : index === 1 ? '💬' : '🎯'}
              </Text>
              <Text style={{ color: Colors.text.primary }} className="text-center text-2xl font-bold">
                {index === 0 ? '2.4k' : index === 1 ? '856' : '142'}
              </Text>
              <Text style={{ color: Colors.text.secondary }} className="text-center">
                {label}
              </Text>
            </View>
          ))}
        </View>

        {/* Community Posts */}
        <View className="mb-8">
          <View className="flex-row justify-between items-center mb-4">
            <Text style={{ color: Colors.text.primary }} className="text-lg font-medium">Recent Posts</Text>
            <Pressable 
              className="py-2 px-4 rounded-xl shadow-lg shadow-green-900/20" 
              style={{ backgroundColor: Colors.brand.primary }}
              onPress={() => setShowNewPostModal(true)}
            >
              <Text className="text-white font-medium">New Post</Text>
            </Pressable>
          </View>

          {loading ? (
            <View className="flex-1 justify-center items-center py-20">
              <ActivityIndicator size="large" color={Colors.brand.primary} />
              <Text style={{ color: Colors.text.secondary }} className="mt-4">
                Loading posts...
              </Text>
            </View>
          ) : posts.length === 0 ? (
            <View className="flex-1 justify-center items-center py-20">
              <Text className="text-6xl mb-4">📝</Text>
              <Text style={{ color: Colors.text.primary }} className="text-xl font-bold mb-2">
                No posts yet
              </Text>
              <Text style={{ color: Colors.text.secondary }} className="text-center">
                Be the first to share your journey with the community!
              </Text>
            </View>
          ) : (
            posts.map((post) => (
              <CommunityPost
                key={post.id}
                {...post}
                onLike={handleLike}
                onSupport={handleSupport}
                onComment={handleComment}
              />
            ))
          )}
        </View>
      </View>

      <NewPostModal
        visible={showNewPostModal}
        onClose={() => setShowNewPostModal(false)}
        onPostCreated={handlePostCreated}
      />

      <CommentsModal
        visible={showCommentsModal}
        onClose={() => setShowCommentsModal(false)}
        postId={selectedPostId}
      />
    </ScrollView>
  );
};

export default Community;

