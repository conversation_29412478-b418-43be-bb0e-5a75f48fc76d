import { useEffect, useState, useCallback } from 'react';
import { Session, User } from '@supabase/supabase-js';
import { supabase } from '../lib/supabase';
import { tokenManager } from '../utils/tokenManager';
import { useRouter } from 'expo-router';

interface AuthState {
  user: User | null;
  session: Session | null;
  loading: boolean;
  isAuthenticated: boolean;
}

interface AuthActions {
  signOut: () => Promise<void>;
  refreshSession: () => Promise<Session | null>;
  checkAuthStatus: () => Promise<boolean>;
}

export const useAuthWithRefresh = (): AuthState & AuthActions => {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    session: null,
    loading: true,
    isAuthenticated: false,
  });

  const router = useRouter();

  /**
   * Update auth state
   */
  const updateAuthState = useCallback((session: Session | null) => {
    setAuthState({
      user: session?.user || null,
      session,
      loading: false,
      isAuthenticated: !!session,
    });
  }, []);

  /**
   * Initialize authentication state
   */
  const initializeAuth = useCallback(async () => {
    try {
      console.log('🔐 Initializing authentication...');
      
      // Get valid session (will refresh if needed)
      const session = await tokenManager.getValidSession();
      
      if (session) {
        console.log('✅ Valid session found');
        updateAuthState(session);
      } else {
        console.log('❌ No valid session found');
        updateAuthState(null);
      }
    } catch (error) {
      console.error('❌ Error initializing auth:', error);
      updateAuthState(null);
    }
  }, [updateAuthState]);

  /**
   * Check authentication status
   */
  const checkAuthStatus = useCallback(async (): Promise<boolean> => {
    try {
      const isAuth = await tokenManager.isAuthenticated();
      
      if (isAuth) {
        const session = await tokenManager.getValidSession();
        updateAuthState(session);
      } else {
        updateAuthState(null);
      }
      
      return isAuth;
    } catch (error) {
      console.error('❌ Error checking auth status:', error);
      updateAuthState(null);
      return false;
    }
  }, [updateAuthState]);

  /**
   * Refresh the current session
   */
  const refreshSession = useCallback(async (): Promise<Session | null> => {
    try {
      console.log('🔄 Manually refreshing session...');
      const session = await tokenManager.refreshToken();
      
      if (session) {
        updateAuthState(session);
        return session;
      } else {
        updateAuthState(null);
        return null;
      }
    } catch (error) {
      console.error('❌ Error refreshing session:', error);
      updateAuthState(null);
      return null;
    }
  }, [updateAuthState]);

  /**
   * Sign out user
   */
  const signOut = useCallback(async (): Promise<void> => {
    try {
      console.log('👋 Signing out...');
      await tokenManager.signOut();
      updateAuthState(null);
      
      // Redirect to login page
      router.replace('/login');
    } catch (error) {
      console.error('❌ Error signing out:', error);
      // Still update state even if sign out fails
      updateAuthState(null);
      router.replace('/login');
    }
  }, [updateAuthState, router]);

  /**
   * Handle auth state changes from Supabase
   */
  useEffect(() => {
    console.log('🔐 Setting up auth state listener...');
    
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('🔐 Auth state changed:', event, session?.user?.id);
        
        switch (event) {
          case 'SIGNED_IN':
            if (session) {
              await tokenManager.saveTokens(session);
              updateAuthState(session);
            }
            break;
            
          case 'SIGNED_OUT':
            await tokenManager.clearTokens();
            updateAuthState(null);
            break;
            
          case 'TOKEN_REFRESHED':
            if (session) {
              await tokenManager.saveTokens(session);
              updateAuthState(session);
            }
            break;
            
          case 'USER_UPDATED':
            if (session) {
              await tokenManager.saveTokens(session);
              updateAuthState(session);
            }
            break;
            
          default:
            // For other events, check if we have a valid session
            if (session) {
              updateAuthState(session);
            }
            break;
        }
      }
    );

    // Initialize auth state
    initializeAuth();

    return () => {
      console.log('🔐 Cleaning up auth state listener...');
      subscription.unsubscribe();
    };
  }, [initializeAuth, updateAuthState]);

  /**
   * Set up automatic token refresh
   */
  useEffect(() => {
    if (!authState.isAuthenticated || !authState.session) {
      return;
    }

    const session = authState.session;
    const expiresAt = session.expires_at || 0;
    const now = Math.floor(Date.now() / 1000);
    const timeUntilExpiry = (expiresAt - now) * 1000; // Convert to milliseconds
    const refreshTime = Math.max(timeUntilExpiry - (5 * 60 * 1000), 60000); // Refresh 5 minutes before expiry, minimum 1 minute

    console.log(`⏰ Setting up auto-refresh in ${Math.floor(refreshTime / 1000)} seconds`);

    const refreshTimer = setTimeout(async () => {
      console.log('⏰ Auto-refreshing token...');
      await refreshSession();
    }, refreshTime);

    return () => {
      clearTimeout(refreshTimer);
    };
  }, [authState.session, authState.isAuthenticated, refreshSession]);

  return {
    ...authState,
    signOut,
    refreshSession,
    checkAuthStatus,
  };
};

// Utility hook for components that just need user ID
export const useUserId = (): string | null => {
  const { user } = useAuthWithRefresh();
  return user?.id || null;
};

// Utility hook for components that need to ensure authentication
export const useRequireAuth = () => {
  const auth = useAuthWithRefresh();
  const router = useRouter();

  useEffect(() => {
    if (!auth.loading && !auth.isAuthenticated) {
      console.log('🔐 Authentication required, redirecting to login...');
      router.replace('/login');
    }
  }, [auth.loading, auth.isAuthenticated, router]);

  return auth;
};
