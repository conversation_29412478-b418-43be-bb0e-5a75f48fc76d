import { supabase } from '../lib/supabase';
import { tokenManager } from './tokenManager';
import { PostgrestBuilder, PostgrestFilterBuilder, PostgrestQueryBuilder } from '@supabase/postgrest-js';

/**
 * Enhanced Supabase client that automatically handles token refresh
 */
class SupabaseWithRefresh {
  private static instance: SupabaseWithRefresh;

  private constructor() {}

  static getInstance(): SupabaseWithRefresh {
    if (!SupabaseWithRefresh.instance) {
      SupabaseWithRefresh.instance = new SupabaseWithRefresh();
    }
    return SupabaseWithRefresh.instance;
  }

  /**
   * Ensure we have a valid session before making API calls
   */
  private async ensureValidSession(): Promise<boolean> {
    try {
      const session = await tokenManager.getValidSession();
      return !!session;
    } catch (error) {
      console.error('❌ Error ensuring valid session:', error);
      return false;
    }
  }

  /**
   * Execute a database operation with automatic token refresh
   */
  private async executeWithRefresh<T>(
    operation: () => Promise<{ data: T | null; error: any }>
  ): Promise<{ data: T | null; error: any }> {
    try {
      // First attempt
      let result = await operation();
      
      // If we get an auth error, try to refresh and retry once
      if (result.error && this.isAuthError(result.error)) {
        console.log('🔄 Auth error detected, attempting token refresh...');
        
        const hasValidSession = await this.ensureValidSession();
        if (hasValidSession) {
          console.log('✅ Token refreshed, retrying operation...');
          result = await operation();
        } else {
          console.log('❌ Token refresh failed');
          return {
            data: null,
            error: {
              message: 'Authentication failed. Please log in again.',
              code: 'AUTH_REFRESH_FAILED'
            }
          };
        }
      }
      
      return result;
    } catch (error) {
      console.error('❌ Unexpected error in executeWithRefresh:', error);
      return {
        data: null,
        error: {
          message: 'An unexpected error occurred',
          code: 'UNEXPECTED_ERROR'
        }
      };
    }
  }

  /**
   * Check if an error is authentication-related
   */
  private isAuthError(error: any): boolean {
    if (!error) return false;
    
    const authErrorCodes = [
      'PGRST301', // JWT expired
      'PGRST302', // JWT invalid
      '401',      // Unauthorized
    ];
    
    const authErrorMessages = [
      'JWT expired',
      'invalid JWT',
      'JWT is expired',
      'invalid token',
      'unauthorized',
      'authentication required',
    ];
    
    const errorCode = error.code?.toString();
    const errorMessage = error.message?.toLowerCase() || '';
    
    return authErrorCodes.includes(errorCode) || 
           authErrorMessages.some(msg => errorMessage.includes(msg));
  }

  /**
   * Enhanced from() method with automatic token refresh
   */
  from<T = any>(table: string) {
    const originalFrom = supabase.from<T>(table);
    
    return {
      select: (columns?: string) => this.wrapQuery(originalFrom.select(columns)),
      insert: (values: any) => this.wrapQuery(originalFrom.insert(values)),
      update: (values: any) => this.wrapQuery(originalFrom.update(values)),
      upsert: (values: any) => this.wrapQuery(originalFrom.upsert(values)),
      delete: () => this.wrapQuery(originalFrom.delete()),
    };
  }

  /**
   * Wrap query builders with automatic refresh
   */
  private wrapQuery<T>(query: any): any {
    // Create a proxy that intercepts method calls and promise resolution
    return new Proxy(query, {
      get: (target, prop) => {
        if (prop === 'then' || prop === 'catch' || prop === 'finally') {
          // Handle promise methods - execute with refresh
          return (...args: any[]) => {
            return this.executeWithRefresh(() => target)[prop](...args);
          };
        }

        // For method calls, wrap the result if it's chainable
        if (typeof target[prop] === 'function') {
          return (...args: any[]) => {
            const result = target[prop](...args);
            // If the result looks like a query builder, wrap it
            if (result && typeof result === 'object' && (result.then || result.select || result.insert)) {
              return this.wrapQuery(result);
            }
            return result;
          };
        }

        // For other properties, return as-is
        return target[prop];
      }
    });
  }

  /**
   * Enhanced auth methods
   */
  get auth() {
    return {
      ...supabase.auth,
      
      // Override signInWithPassword to save tokens
      signInWithPassword: async (credentials: { email: string; password: string }) => {
        const result = await supabase.auth.signInWithPassword(credentials);
        
        if (result.data.session) {
          await tokenManager.saveTokens(result.data.session);
        }
        
        return result;
      },
      
      // Override signUp to save tokens
      signUp: async (credentials: any) => {
        const result = await supabase.auth.signUp(credentials);
        
        if (result.data.session) {
          await tokenManager.saveTokens(result.data.session);
        }
        
        return result;
      },
      
      // Override signInWithIdToken to save tokens
      signInWithIdToken: async (credentials: any) => {
        const result = await supabase.auth.signInWithIdToken(credentials);
        
        if (result.data.session) {
          await tokenManager.saveTokens(result.data.session);
        }
        
        return result;
      },
      
      // Enhanced getSession that ensures validity
      getSession: async () => {
        const session = await tokenManager.getValidSession();
        return { data: { session }, error: null };
      },
      
      // Enhanced signOut
      signOut: async () => {
        await tokenManager.signOut();
        return { error: null };
      },
    };
  }

  /**
   * Storage methods (pass through for now)
   */
  get storage() {
    return supabase.storage;
  }

  /**
   * Realtime methods (pass through for now)
   */
  get realtime() {
    return supabase.realtime;
  }

  /**
   * Functions methods (pass through for now)
   */
  get functions() {
    return supabase.functions;
  }
}

// Export singleton instance
export const supabaseWithRefresh = SupabaseWithRefresh.getInstance();

// Export for backward compatibility
export { supabase } from '../lib/supabase';
