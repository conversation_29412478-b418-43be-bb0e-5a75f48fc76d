{"name": "gifted-charts-core", "version": "0.1.65", "description": "Mathematical and logical utilities used by react-gifted-charts and react-native-gifted-charts", "main": "./dist/index.js", "files": ["dist"], "scripts": {"build": "rm -rf ./dist && tsc -p . && ./build.sh", "test": "echo \"Error: no test specified\" && exit 1", "lint": "eslint src/**/*.ts"}, "devDependencies": {"@testing-library/jest-dom": "^6.4.8", "@testing-library/react": "^16.0.0", "@testing-library/user-event": "^14.5.2", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/react-native": "^0.73.0", "@typescript-eslint/eslint-plugin": "^7.17.0", "eslint": "^8.56.0", "eslint-config-love": "^62.0.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-n": "^16.6.2", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-react": "^7.33.2", "typescript": "^5.5.4"}, "peerDependencies": {"react": "*", "react-native": "*", "react-native-svg": "*"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "repository": "https://github.com/Ab<PERSON>anda<PERSON>-<PERSON><PERSON><PERSON><PERSON>/gifted-charts-core", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/A<PERSON><PERSON>anda<PERSON>-<PERSON>)", "license": "MIT", "bugs": {"url": "https://github.com/Ab<PERSON>anda<PERSON>-<PERSON><PERSON><PERSON><PERSON>/gifted-charts-core/issues"}, "homepage": "https://github.com/Ab<PERSON>anda<PERSON>-<PERSON><PERSON><PERSON><PERSON>/gifted-charts-core#readme", "publishConfig": {"registry": "https://registry.npmjs.org/"}, "keywords": ["gifted-charts", "react-gifted-charts", "react-native-gifted-charts"]}