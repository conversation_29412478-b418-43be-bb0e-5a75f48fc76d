{"name": "@react-navigation/native-stack", "description": "Native stack navigator using react-native-screens", "version": "7.3.13", "keywords": ["react-native-component", "react-component", "react-native", "react-navigation", "ios", "android", "native", "stack"], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/react-navigation/react-navigation.git", "directory": "packages/native-stack"}, "bugs": {"url": "https://github.com/software-mansion/react-native-screens/issues"}, "homepage": "https://github.com/software-mansion/react-native-screens#readme", "source": "./src/index.tsx", "main": "./lib/module/index.js", "types": "./lib/typescript/src/index.d.ts", "exports": {".": {"types": "./lib/typescript/src/index.d.ts", "default": "./lib/module/index.js"}, "./package.json": "./package.json"}, "files": ["src", "lib", "!**/__tests__"], "sideEffects": false, "publishConfig": {"access": "public"}, "scripts": {"prepack": "bob build", "clean": "del lib"}, "dependencies": {"@react-navigation/elements": "^2.4.2", "warn-once": "^0.1.1"}, "devDependencies": {"@jest/globals": "^29.7.0", "@react-navigation/native": "^7.1.9", "@testing-library/react-native": "^13.2.0", "@types/react": "~19.0.10", "del-cli": "^6.0.0", "react": "19.0.0", "react-native": "0.79.2", "react-native-builder-bob": "^0.40.9", "react-native-screens": "~4.10.0", "react-test-renderer": "19.0.0", "typescript": "^5.8.3"}, "peerDependencies": {"@react-navigation/native": "^7.1.9", "react": ">= 18.2.0", "react-native": "*", "react-native-safe-area-context": ">= 4.0.0", "react-native-screens": ">= 4.0.0"}, "react-native-builder-bob": {"source": "src", "output": "lib", "targets": [["module", {"esm": true}], ["typescript", {"project": "tsconfig.build.json"}]]}, "gitHead": "9805f3b244eab19148a61bc888fc58e3d674cd2b"}