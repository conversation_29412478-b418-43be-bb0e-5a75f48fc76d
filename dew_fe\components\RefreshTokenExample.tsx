import React from 'react';
import { View, Text, TouchableOpacity, Alert } from 'react-native';
import { useAuthWithRefresh } from '../hooks/useAuthWithRefresh';
import { supabaseWithRefresh } from '../utils/supabaseWithRefresh';
import { tokenManager } from '../utils/tokenManager';
import Colors from '../constants/Colors';

/**
 * Example component demonstrating the refresh token API integration
 * This shows how to use the enhanced authentication system
 */
const RefreshTokenExample: React.FC = () => {
  const { user, session, loading, isAuthenticated, signOut, refreshSession, checkAuthStatus } = useAuthWithRefresh();

  const handleManualRefresh = async () => {
    try {
      console.log('🔄 Manually refreshing token...');
      const newSession = await refreshSession();
      
      if (newSession) {
        Alert.alert('Success', 'Token refreshed successfully!');
      } else {
        Alert.alert('Error', 'Failed to refresh token');
      }
    } catch (error) {
      console.error('❌ Manual refresh error:', error);
      Alert.alert('Error', 'An error occurred while refreshing token');
    }
  };

  const handleCheckAuth = async () => {
    try {
      console.log('🔐 Checking authentication status...');
      const isAuth = await checkAuthStatus();
      Alert.alert('Auth Status', `Authenticated: ${isAuth}`);
    } catch (error) {
      console.error('❌ Auth check error:', error);
      Alert.alert('Error', 'An error occurred while checking auth status');
    }
  };

  const handleTestApiCall = async () => {
    try {
      console.log('🧪 Testing API call with automatic refresh...');
      
      // This will automatically refresh the token if needed
      const { data, error } = await supabaseWithRefresh
        .from('posts')
        .select('*')
        .limit(1);

      if (error) {
        Alert.alert('API Error', error.message);
      } else {
        Alert.alert('API Success', `Retrieved ${data?.length || 0} posts`);
      }
    } catch (error) {
      console.error('❌ API test error:', error);
      Alert.alert('Error', 'An error occurred during API test');
    }
  };

  const handleClearTokens = async () => {
    try {
      console.log('🧹 Clearing all tokens...');
      await tokenManager.clearTokens();
      Alert.alert('Success', 'All tokens cleared');
    } catch (error) {
      console.error('❌ Clear tokens error:', error);
      Alert.alert('Error', 'An error occurred while clearing tokens');
    }
  };

  const handleSignOut = async () => {
    try {
      await signOut();
      Alert.alert('Success', 'Signed out successfully');
    } catch (error) {
      console.error('❌ Sign out error:', error);
      Alert.alert('Error', 'An error occurred during sign out');
    }
  };

  if (loading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: Colors.background.primary }}>
        <Text style={{ color: Colors.text.primary }}>Loading...</Text>
      </View>
    );
  }

  return (
    <View style={{ flex: 1, padding: 20, backgroundColor: Colors.background.primary }}>
      <Text style={{ color: Colors.text.primary, fontSize: 24, fontWeight: 'bold', marginBottom: 20 }}>
        Refresh Token API Demo
      </Text>

      {/* Auth Status */}
      <View style={{ marginBottom: 20, padding: 15, backgroundColor: Colors.background.elevated, borderRadius: 10 }}>
        <Text style={{ color: Colors.text.primary, fontSize: 18, marginBottom: 10 }}>Authentication Status</Text>
        <Text style={{ color: Colors.text.secondary }}>Authenticated: {isAuthenticated ? '✅ Yes' : '❌ No'}</Text>
        <Text style={{ color: Colors.text.secondary }}>User ID: {user?.id || 'None'}</Text>
        <Text style={{ color: Colors.text.secondary }}>Email: {user?.email || 'None'}</Text>
        <Text style={{ color: Colors.text.secondary }}>
          Token Expires: {session?.expires_at ? new Date(session.expires_at * 1000).toLocaleString() : 'None'}
        </Text>
      </View>

      {/* Action Buttons */}
      <View style={{ gap: 15 }}>
        <TouchableOpacity
          onPress={handleManualRefresh}
          style={{ backgroundColor: Colors.brand.primary, padding: 15, borderRadius: 10 }}
          disabled={!isAuthenticated}
        >
          <Text style={{ color: 'white', textAlign: 'center', fontWeight: 'bold' }}>
            Manual Token Refresh
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={handleCheckAuth}
          style={{ backgroundColor: Colors.status.info, padding: 15, borderRadius: 10 }}
        >
          <Text style={{ color: 'white', textAlign: 'center', fontWeight: 'bold' }}>
            Check Auth Status
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={handleTestApiCall}
          style={{ backgroundColor: Colors.status.warning, padding: 15, borderRadius: 10 }}
          disabled={!isAuthenticated}
        >
          <Text style={{ color: 'white', textAlign: 'center', fontWeight: 'bold' }}>
            Test API Call (Auto Refresh)
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={handleClearTokens}
          style={{ backgroundColor: Colors.status.error, padding: 15, borderRadius: 10 }}
        >
          <Text style={{ color: 'white', textAlign: 'center', fontWeight: 'bold' }}>
            Clear All Tokens
          </Text>
        </TouchableOpacity>

        {isAuthenticated && (
          <TouchableOpacity
            onPress={handleSignOut}
            style={{ backgroundColor: Colors.background.secondary, padding: 15, borderRadius: 10, borderWidth: 1, borderColor: Colors.status.error }}
          >
            <Text style={{ color: Colors.status.error, textAlign: 'center', fontWeight: 'bold' }}>
              Sign Out
            </Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Usage Instructions */}
      <View style={{ marginTop: 30, padding: 15, backgroundColor: Colors.background.elevated, borderRadius: 10 }}>
        <Text style={{ color: Colors.text.primary, fontSize: 16, fontWeight: 'bold', marginBottom: 10 }}>
          How to Use:
        </Text>
        <Text style={{ color: Colors.text.secondary, lineHeight: 20 }}>
          • Manual Token Refresh: Manually refresh your access token{'\n'}
          • Check Auth Status: Verify if you're authenticated{'\n'}
          • Test API Call: Make an API call that will auto-refresh if needed{'\n'}
          • Clear All Tokens: Remove all stored tokens{'\n'}
          • Sign Out: Sign out and clear all tokens
        </Text>
      </View>
    </View>
  );
};

export default RefreshTokenExample;
