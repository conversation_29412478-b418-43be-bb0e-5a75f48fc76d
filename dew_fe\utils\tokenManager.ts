import * as SecureStore from 'expo-secure-store';
import { supabase } from '../lib/supabase';
import { Session, User } from '@supabase/supabase-js';

interface TokenData {
  access_token: string;
  refresh_token: string;
  expires_at: number;
  user: User;
}

export class TokenManager {
  private static instance: TokenManager;
  private refreshPromise: Promise<Session | null> | null = null;
  private isRefreshing = false;

  private constructor() {}

  static getInstance(): TokenManager {
    if (!TokenManager.instance) {
      TokenManager.instance = new TokenManager();
    }
    return TokenManager.instance;
  }

  /**
   * Save tokens to secure storage
   */
  async saveTokens(session: Session): Promise<void> {
    try {
      const tokenData: TokenData = {
        access_token: session.access_token,
        refresh_token: session.refresh_token,
        expires_at: session.expires_at || 0,
        user: session.user,
      };

      await Promise.all([
        SecureStore.setItemAsync('access_token', session.access_token),
        SecureStore.setItemAsync('refresh_token', session.refresh_token),
        SecureStore.setItemAsync('expires_at', session.expires_at?.toString() || '0'),
        SecureStore.setItemAsync('user_id', session.user.id),
        SecureStore.setItemAsync('user_email', session.user.email || ''),
        SecureStore.setItemAsync('user_name', session.user.user_metadata?.full_name || ''),
        SecureStore.setItemAsync('user_avatar', session.user.user_metadata?.avatar_url || ''),
        SecureStore.setItemAsync('user_created_at', session.user.created_at || ''),
        SecureStore.setItemAsync('user_updated_at', session.user.updated_at || ''),
      ]);

      console.log('✅ Tokens saved successfully');
    } catch (error) {
      console.error('❌ Error saving tokens:', error);
      throw error;
    }
  }

  /**
   * Get stored tokens from secure storage
   */
  async getStoredTokens(): Promise<TokenData | null> {
    try {
      const [
        access_token,
        refresh_token,
        expires_at,
        user_id,
        user_email,
        user_name,
        user_avatar,
        user_created_at,
        user_updated_at,
      ] = await Promise.all([
        SecureStore.getItemAsync('access_token'),
        SecureStore.getItemAsync('refresh_token'),
        SecureStore.getItemAsync('expires_at'),
        SecureStore.getItemAsync('user_id'),
        SecureStore.getItemAsync('user_email'),
        SecureStore.getItemAsync('user_name'),
        SecureStore.getItemAsync('user_avatar'),
        SecureStore.getItemAsync('user_created_at'),
        SecureStore.getItemAsync('user_updated_at'),
      ]);

      if (!access_token || !refresh_token || !user_id) {
        return null;
      }

      return {
        access_token,
        refresh_token,
        expires_at: parseInt(expires_at || '0'),
        user: {
          id: user_id,
          email: user_email || '',
          user_metadata: {
            full_name: user_name || '',
            avatar_url: user_avatar || '',
          },
          created_at: user_created_at || '',
          updated_at: user_updated_at || '',
        } as User,
      };
    } catch (error) {
      console.error('❌ Error getting stored tokens:', error);
      return null;
    }
  }

  /**
   * Check if the current token is expired or will expire soon
   */
  isTokenExpired(expiresAt: number, bufferMinutes: number = 5): boolean {
    const now = Math.floor(Date.now() / 1000);
    const buffer = bufferMinutes * 60; // Convert minutes to seconds
    return now >= (expiresAt - buffer);
  }

  /**
   * Refresh the access token using the refresh token
   */
  async refreshToken(): Promise<Session | null> {
    // Prevent multiple simultaneous refresh attempts
    if (this.isRefreshing && this.refreshPromise) {
      console.log('🔄 Token refresh already in progress, waiting...');
      return this.refreshPromise;
    }

    this.isRefreshing = true;
    this.refreshPromise = this.performTokenRefresh();

    try {
      const result = await this.refreshPromise;
      return result;
    } finally {
      this.isRefreshing = false;
      this.refreshPromise = null;
    }
  }

  private async performTokenRefresh(): Promise<Session | null> {
    try {
      console.log('🔄 Starting token refresh...');
      
      const storedTokens = await this.getStoredTokens();
      if (!storedTokens?.refresh_token) {
        console.log('❌ No refresh token found');
        await this.clearTokens();
        return null;
      }

      // Use Supabase's built-in refresh session method
      const { data, error } = await supabase.auth.refreshSession({
        refresh_token: storedTokens.refresh_token,
      });

      if (error) {
        console.error('❌ Token refresh failed:', error.message);
        
        // If refresh fails, clear stored tokens
        if (error.message.includes('Invalid refresh token') || 
            error.message.includes('refresh_token_not_found')) {
          console.log('🧹 Clearing invalid tokens');
          await this.clearTokens();
        }
        
        return null;
      }

      if (data.session) {
        console.log('✅ Token refreshed successfully');
        await this.saveTokens(data.session);
        return data.session;
      }

      return null;
    } catch (error) {
      console.error('❌ Unexpected error during token refresh:', error);
      return null;
    }
  }

  /**
   * Get a valid session, refreshing if necessary
   */
  async getValidSession(): Promise<Session | null> {
    try {
      // First, try to get the current session from Supabase
      const { data: { session }, error } = await supabase.auth.getSession();
      
      if (error) {
        console.error('❌ Error getting current session:', error);
      }

      // If we have a valid session, return it
      if (session && !this.isTokenExpired(session.expires_at || 0)) {
        return session;
      }

      // If session is expired or doesn't exist, try to refresh
      console.log('🔄 Session expired or missing, attempting refresh...');
      const refreshedSession = await this.refreshToken();
      
      if (refreshedSession) {
        return refreshedSession;
      }

      // If refresh failed, clear tokens and return null
      await this.clearTokens();
      return null;
    } catch (error) {
      console.error('❌ Error getting valid session:', error);
      return null;
    }
  }

  /**
   * Clear all stored tokens
   */
  async clearTokens(): Promise<void> {
    try {
      const keys = [
        'access_token',
        'refresh_token',
        'expires_at',
        'user_id',
        'user_email',
        'user_name',
        'user_avatar',
        'user_created_at',
        'user_updated_at',
      ];

      await Promise.all(keys.map(key => SecureStore.deleteItemAsync(key)));
      console.log('🧹 All tokens cleared');
    } catch (error) {
      console.error('❌ Error clearing tokens:', error);
    }
  }

  /**
   * Sign out and clear all tokens
   */
  async signOut(): Promise<void> {
    try {
      await supabase.auth.signOut();
      await this.clearTokens();
      console.log('👋 Signed out successfully');
    } catch (error) {
      console.error('❌ Error during sign out:', error);
      // Still clear tokens even if Supabase signOut fails
      await this.clearTokens();
    }
  }

  /**
   * Check if user is authenticated with valid tokens
   */
  async isAuthenticated(): Promise<boolean> {
    const session = await this.getValidSession();
    return session !== null;
  }
}

// Export singleton instance
export const tokenManager = TokenManager.getInstance();
