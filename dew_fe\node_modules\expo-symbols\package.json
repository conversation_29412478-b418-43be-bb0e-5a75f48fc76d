{"name": "expo-symbols", "version": "0.4.5", "description": "Provides access to the SF Symbols library on iOS for React Native and Expo apps.", "main": "build/index.js", "types": "build/index.d.ts", "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "keywords": ["react-native", "expo", "expo-symbols", "symbols", "sfsymbols", "ios"], "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-symbols"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "author": "650 Industries, Inc.", "license": "MIT", "homepage": "https://docs.expo.dev/versions/latest/sdk/symbols/", "dependencies": {"sf-symbols-typescript": "^2.0.0"}, "devDependencies": {"expo-module-scripts": "^4.1.7"}, "peerDependencies": {"expo": "*", "react-native": "*"}, "gitHead": "7638c800b57fe78f57cc7f129022f58e84a523c5"}